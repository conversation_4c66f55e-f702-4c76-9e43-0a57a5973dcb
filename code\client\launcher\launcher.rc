/*
 * This file is part of the CitizenFX project - http://citizen.re/
 *
 * See L<PERSON>EN<PERSON> and MENTIONS in the root of the source tree for information
 * regarding licensing.
 */

#include "launcher.rc.h"
#include <windows.h>

#include <launcher_version.h>

LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
#ifdef GTA_NY
IDI_ICON1               ICON                    "citizeniv.ico"
#elif defined(GTA_FIVE)
IDI_ICON1               ICON                    "fivem.ico"
#elif defined(IS_RDR3)
IDI_ICON1               ICON                    "redm.ico"
#endif

#ifdef LAUNCHER_PERSONALITY_MAIN
IDI_SNAIL				ICON					"snail.ico"

#if defined(GTA_FIVE)
IDI_SP					ICON					"fivem_sp.ico"
IDI_SDK					ICON					"fivem_sdk.ico"
#endif
#endif

1						RT_MANIFEST				"launcher.manifest"

#ifdef LAUNCHER_PERSONALITY_MAIN
IDM_BACKDROP			MEOW					"5fe2788f5ec1541b0ecfeafec5838ce3.jpg"
#endif

STRINGTABLE
BEGIN
#if defined(GTA_FIVE)
	IDS_FOLDER_NAME, L"Dữ liệu Ứng dụng FiveM"
#elif defined(IS_RDR3)
	IDS_FOLDER_NAME, L"Dữ liệu Ứng dụng RedM"
#else
	IDS_FOLDER_NAME, L"Dữ liệu Ứng dụng Cfx.re"
#endif
END

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

#if defined(LAUNCHER_PERSONALITY_CHROME)
#define SUFFIX " Chromium subprocess"
#elif defined(LAUNCHER_PERSONALITY_GAME_MTL)
#define SUFFIX " RGL subprocess"
#elif defined(LAUNCHER_PERSONALITY_ANY_GAME)
#define SUFFIX " Game subprocess"
#else
#define SUFFIX ""
#endif

#if defined(GTA_FIVE)
#define PRODUCT "FiveM"
#elif defined(IS_RDR3)
#define PRODUCT "RedM"
#elif defined(GTA_NY)
#define PRODUCT "LibertyM"
#else
#define PRODUCT "CitizenFX"
#endif

#define STR_HELPER(x) #x
#define STR(x) STR_HELPER(x)

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 2,0,0,EXE_VERSION
 PRODUCTVERSION 2,0,0,EXE_VERSION
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Cfx.re"
			VALUE "FileDescription", PRODUCT SUFFIX
            VALUE "InternalName", PRODUCT
            VALUE "FileVersion", "2.0.0." STR(EXE_VERSION)
            VALUE "LegalCopyright", "(C) 2015-2022 Cfx.re"
            VALUE "OriginalFilename", "CitizenMP.exe"
            VALUE "ProductName", PRODUCT
            VALUE "ProductVersion", "2.0.0." STR(EXE_VERSION)
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
